package com.eddars.rockpaperscissors.gesture

import android.Manifest
import android.content.pm.PackageManager
import android.util.Size
import android.util.Log
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.EventChannel
import java.util.concurrent.Executors
import java.nio.ByteBuffer

// Imports MediaPipe
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.framework.image.MediaImageBuilder
import android.os.SystemClock
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.core.Delegate
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.core.ImageProcessingOptions
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizer
import com.google.mediapipe.tasks.vision.gesturerecognizer.GestureRecognizerResult
import android.graphics.Bitmap
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.graphics.YuvImage
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import java.io.FileOutputStream

class MpGestureManager(private val activity: FlutterActivity) : EventChannel.StreamHandler {

    private var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>? = null
    private var analysis: ImageAnalysis? = null
    private var gestureRecognizer: GestureRecognizer? = null
    private var sink: EventChannel.EventSink? = null
    private var minScore: Double = 0.1  // Seuil de confiance minimum - encore plus bas pour une détection ultra-rapide
    private var useFrontCamera: Boolean = true

    // Utiliser un exécuteur monothread pour une latence plus basse et stable
    private val cameraExecutor = Executors.newSingleThreadExecutor()

    // Variables pour le suivi des frames
    private var frameCount = 0L
    private var lastFpsTs = System.currentTimeMillis()

    fun start() {
        Log.d("MpGestureManager", "🚀 START: Method called")
        Log.d("MpGestureManager", "📷 INFO: Configuration actuelle - useFrontCamera: $useFrontCamera, minScore: $minScore")
        try {
            Log.d("MpGestureManager", "🔍 TENTATIVE: Vérification des permissions et démarrage de la caméra...")
            ensurePermissionAndStart()
            Log.d("MpGestureManager", "✅ SUCCÈS: Méthode ensurePermissionAndStart appelée sans exception")
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ START ERROR: ${e.message}")
            Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
        }
    }

    fun setMinScore(minScore: Double) {
        this.minScore = minScore
    }

    fun setUseFrontCamera(useFrontCamera: Boolean) {
        val wasRunning = cameraProviderFuture != null && analysis != null
        val oldValue = this.useFrontCamera
        this.useFrontCamera = useFrontCamera

        Log.d("MpGestureManager", "🔄 CAMERA: Changement de caméra demandé: $oldValue -> $useFrontCamera, caméra en cours: $wasRunning")

        // Si la caméra était en cours d'exécution, l'arrêter et la redémarrer uniquement si la configuration change
        // Comme nous utilisons uniquement la caméra frontale, éviter les redémarrages inutiles
        if (wasRunning && oldValue != useFrontCamera && useFrontCamera) {
            Log.d("MpGestureManager", "🔄 CAMERA: Redémarrage de la caméra avec la nouvelle configuration")
            // Arrêter seulement la caméra mais conserver le modèle
            stopCameraOnly()
            // Redémarrer avec un délai pour laisser le temps à l'arrêt de se terminer
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                start()
            }, 500)
        }
    }

    fun stopCameraOnly() {
        Log.d("MpGestureManager", "🛑 STOP: Arrêt de la caméra uniquement")
        try {
            val provider = cameraProviderFuture?.get()
            provider?.unbindAll()
            Log.d("MpGestureManager", "✅ STOP: Caméra déliée avec succès")
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ STOP: Erreur lors de l'arrêt de la caméra: ${e.message}")
        }
        analysis = null
        Log.d("MpGestureManager", "✅ STOP: Caméra arrêtée, modèle conservé")
    }

    fun stop() {
        Log.d("MpGestureManager", "🛑 STOP: Arrêt de la caméra et simulation")

        // Arrêter la simulation si elle est active
        stopSimulationMode()

        try {
            val provider = cameraProviderFuture?.get()
            provider?.unbindAll()
            Log.d("MpGestureManager", "✅ STOP: Caméra déliée avec succès")
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ STOP: Erreur lors de l'arrêt de la caméra: ${e.message}")
        }
        analysis = null
        // Ne pas fermer le gestureRecognizer ici, il sera réutilisé au prochain démarrage
        Log.d("MpGestureManager", "✅ STOP: Caméra arrêtée, modèle conservé")
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
        sink = events
    }

    override fun onCancel(arguments: Any?) {
        sink = null
    }

    fun onRequestPermissionsResult(requestCode: Int, grantResults: IntArray) {
        if (requestCode == 9001 && grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startCameraAndRecognizer()
        }
    }

    private fun ensurePermissionAndStart() {
        try {
            Log.d("MpGestureManager", "🔐 PERMISSION: Checking camera permission...")
            val granted = ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
            if (!granted) {
                Log.e("MpGestureManager", "❌ PERMISSION: Camera permission not granted. Please grant permission in app settings.")
                return
            } else {
                Log.d("MpGestureManager", "✅ PERMISSION: Camera permission granted, starting camera...")
            }
            // Toujours essayer de démarrer la caméra, que la permission soit accordée ou non
            startCameraAndRecognizer()
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ PERMISSION ERROR: ${e.message}")
            // Essayer de démarrer la caméra même en cas d'erreur
            try {
                startCameraAndRecognizer()
            } catch (e2: Exception) {
                Log.e("MpGestureManager", "❌ PERMISSION ERROR: Échec du démarrage de la caméra: ${e2.message}")
            }
        }
    }

    private fun startCameraAndRecognizer() {
        // INITIALISATION DU MODÈLE MEDIAPIPE
        Log.d("MpGestureManager", "📁 MODEL: Loading MediaPipe gesture recognizer...")

        try {
            // Configuration de base pour MediaPipe avec accélération GPU
            val baseOptions = BaseOptions.builder()
                .setModelAssetPath("models/gesture_recognizer.task")
                .setDelegate(Delegate.GPU) // Activer le délégué GPU pour un traitement beaucoup plus rapide
                .build()

            // Options spécifiques pour le reconnaissance de gestes - Optimisées pour tablettes physiques
            val optionsBuilder = GestureRecognizer.GestureRecognizerOptions.builder()
                .setBaseOptions(baseOptions)
                .setRunningMode(RunningMode.LIVE_STREAM)
                .setNumHands(1)  // Détecter uniquement 1 main pour un traitement plus rapide
                .setMinHandDetectionConfidence(0.1f)  // Seuil très bas pour détecter les mains sur tablettes
                .setMinHandPresenceConfidence(0.1f)  // Seuil très bas pour une meilleure sensibilité
                .setMinTrackingConfidence(0.1f)  // Seuil très bas pour un suivi plus sensible
                .setResultListener { result: GestureRecognizerResult, input: MPImage ->
                    Log.d("MpGestureManager", "🎯 RESULT: Résultat de reconnaissance reçu - Device: ${android.os.Build.MODEL}")
                    Log.d("MpGestureManager", "🎯 RESULT: Format de l'image d'entrée: ${input.width}x${input.height}")

                    // Vérifier si des mains sont détectées
                    val handsDetected = result.handedness().isNotEmpty()
                    val gesturesDetected = result.gestures().isNotEmpty()

                    Log.d("MpGestureManager", "👋 MAINS: ${if (handsDetected) "${result.handedness().size} main(s) détectée(s)" else "Aucune main détectée"}")
                    Log.d("MpGestureManager", "✋ GESTES: ${if (gesturesDetected) "${result.gestures().size} geste(s) détecté(s)" else "Aucun geste détecté"}")

                    // Log détaillé des résultats
                    if (gesturesDetected) {
                        result.gestures().forEachIndexed { index, gestureList ->
                            Log.d("MpGestureManager", "📊 DEBUG: Gesture $index has ${gestureList.size} categories")
                            gestureList.forEach { category ->
                                Log.d("MpGestureManager", "📊 DEBUG: Category: ${category.categoryName()}, Score: ${category.score()}")
                            }
                        }
                    }
                    
                    // Si des mains sont détectées, afficher des informations supplémentaires
                    if (handsDetected) {
                        try {
                            Log.d("MpGestureManager", "👋 MAINS: ${result.handedness().size} main(s) avec des points de repère détectée(s)")
                        } catch (e: Exception) {
                            Log.e("MpGestureManager", "❌ ERREUR lors de l'accès aux points de repère: ${e.message}")
                        }
                    }
                    
                    // Traiter les résultats de reconnaissance
                    if (result.gestures().isNotEmpty() && result.handedness().isNotEmpty()) {
                        try {
                            val gesture = result.gestures()[0][0]
                            val handedness = result.handedness()[0][0]
                            val gestureName = gesture.categoryName()
                            val confidence = gesture.score().toDouble()
                            // Obtenir le nom de la main original
                            val originalHandName = handedness.categoryName()
                            // Pour la caméra frontale, MediaPipe détecte correctement la main (pas besoin d'inverser)
                            // mais l'utilisateur voit une image miroir, donc on inverse pour l'UI
                            val handName = if (useFrontCamera) {
                                // Inverser gauche/droite pour l'affichage en mode miroir
                                if (originalHandName == "Left") "Right" else "Left"
                            } else {
                                originalHandName
                            }

                        Log.d("MpGestureManager", "📷 Geste reconnu: $gestureName avec confiance $confidence par la main: $originalHandName (affiché: $handName)")
                        Log.d("MpGestureManager", "📷 Score minimum: $minScore, Score obtenu: $confidence")

                        // Ignorer les gestes "None" même s'ils ont un score suffisant
                        if (confidence >= minScore && gestureName != "None") {
                            Log.d("MpGestureManager", "📷 ENVOI: Envoi du résultat à Flutter - Device: ${android.os.Build.MODEL}, Gesture: $gestureName, Score: $confidence")
                            // Exécuter sur le thread principal pour éviter les erreurs
                            activity.runOnUiThread {
                                try {
                                    sink?.success(mapOf(
                                        "label" to gestureName,
                                        "score" to confidence,
                                        "hand" to handName,
                                        "ts" to System.currentTimeMillis(),
                                        "device" to android.os.Build.MODEL
                                    ))
                                    Log.d("MpGestureManager", "✅ SUCCESS: Résultat envoyé avec succès à Flutter")
                                } catch (e: Exception) {
                                    Log.e("MpGestureManager", "❌ ERREUR lors de l'envoi à Flutter: ${e.message}")
                                }
                            }
                        } else if (gestureName == "None") {
                            Log.d("MpGestureManager", "📷 IGNORE: Geste \"None\" ignoré même avec score suffisant")
                        } else {
                            Log.d("MpGestureManager", "📷 IGNORE: Score insuffisant ($confidence < $minScore), geste ignoré: $gestureName")
                        }
                        } catch (e: Exception) {
                            Log.e("MpGestureManager", "❌ ERREUR lors du traitement des résultats: ${e.message}")
                            e.printStackTrace()
                        }
                    } else {
                        if (handsDetected) {
                            Log.d("MpGestureManager", "📷 AUCUN: Main(s) détectée(s) mais aucun geste reconnu")
                        } else {
                            Log.d("MpGestureManager", "📷 AUCUN: Aucune main détectée dans ce frame")
                        }
                    }
                }
                .setErrorListener { error: RuntimeException ->
                    Log.e("MpGestureManager", "❌ MediaPipe Error: ${error.message}")
                    error.printStackTrace()
                }

            val options = optionsBuilder.build()

            // Créer le reconnaiseur de gestes
            try {
                gestureRecognizer = GestureRecognizer.createFromOptions(activity, options)
                Log.d("MpGestureManager", "✅ MODEL: MediaPipe gesture recognizer loaded successfully with GPU")
            } catch (e: UnsatisfiedLinkError) {
                Log.e("MpGestureManager", "❌ NATIVE ERROR: MediaPipe native library not found: ${e.message}")
                Log.w("MpGestureManager", "🎭 FALLBACK: Switching to simulation mode due to missing native libraries")
                startSimulationMode()
                return
            } catch (e: Exception) {
                Log.e("MpGestureManager", "❌ MODEL ERROR: Failed to load MediaPipe model with GPU: ${e.message}")
                Log.e("MpGestureManager", "🔄 MODEL: Trying to load with CPU delegate instead...")
                
                try {
                    // Essayer avec le délégué CPU comme alternative
                    val cpuBaseOptions = BaseOptions.builder()
                        .setModelAssetPath("models/gesture_recognizer.task")
                        .setDelegate(Delegate.CPU)
                        .build()
                    
                    val cpuOptionsBuilder = GestureRecognizer.GestureRecognizerOptions.builder()
                        .setBaseOptions(cpuBaseOptions)
                        .setRunningMode(RunningMode.LIVE_STREAM)
                        .setNumHands(1)
                        .setMinHandDetectionConfidence(0.3f)
                        .setMinHandPresenceConfidence(0.3f)
                        .setMinTrackingConfidence(0.3f)
                        .setResultListener { result: GestureRecognizerResult, input: MPImage ->
                            Log.d("MpGestureManager", "🎯 RESULT: Résultat de reconnaissance reçu (CPU)")
                            // Même code de traitement des résultats...
                            
                            // Vérifier si des mains sont détectées
                            val handsDetected = result.handedness().isNotEmpty()
                            
                            Log.d("MpGestureManager", "👋 MAINS: ${if (handsDetected) "${result.handedness().size} main(s) détectée(s)" else "Aucune main détectée"}")
                            
                            // Traiter les résultats de reconnaissance
                            if (result.gestures().isNotEmpty() && result.handedness().isNotEmpty()) {
                                try {
                                    val gesture = result.gestures()[0][0]
                                    val handedness = result.handedness()[0][0]
                                    val gestureName = gesture.categoryName()
                                    val confidence = gesture.score().toDouble()
                                    // Obtenir le nom de la main original
                                    val originalHandName = handedness.categoryName()
                                    // Pour la caméra frontale, MediaPipe détecte correctement la main (pas besoin d'inverser)
                                    // mais l'utilisateur voit une image miroir, donc on inverse pour l'UI
                                    val handName = if (useFrontCamera) {
                                        // Inverser gauche/droite pour l'affichage en mode miroir
                                        if (originalHandName == "Left") "Right" else "Left"
                                    } else {
                                        originalHandName
                                    }
                                    
                                    // Ignorer les gestes "None" même s'ils ont un score suffisant
                                    if (confidence >= minScore && gestureName != "None") {
                                        // Exécuter sur le thread principal pour éviter les erreurs
                                        activity.runOnUiThread {
                                            sink?.success(mapOf(
                                                "label" to gestureName,
                                                "score" to confidence,
                                                "hand" to handName,
                                                "ts" to System.currentTimeMillis()
                                            ))
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e("MpGestureManager", "❌ ERREUR lors du traitement des résultats: ${e.message}")
                                }
                            }
                        }
                        .setErrorListener { error: RuntimeException ->
                            Log.e("MpGestureManager", "❌ MediaPipe Error (CPU): ${error.message}")
                        }
                    
                    val cpuOptions = cpuOptionsBuilder.build()
                    gestureRecognizer = GestureRecognizer.createFromOptions(activity, cpuOptions)
                    Log.d("MpGestureManager", "✅ MODEL: MediaPipe gesture recognizer loaded successfully with CPU")
                } catch (e2: UnsatisfiedLinkError) {
                    Log.e("MpGestureManager", "❌ NATIVE ERROR: MediaPipe native library not found (CPU): ${e2.message}")
                    Log.w("MpGestureManager", "🎭 FALLBACK: Switching to simulation mode due to missing native libraries")
                    startSimulationMode()
                    return
                } catch (e2: Exception) {
                    Log.e("MpGestureManager", "❌ MODEL ERROR: Failed to load MediaPipe model with CPU too: ${e2.message}")
                    Log.e("MpGestureManager", "❌ MODEL ERROR: Stack trace:", e2)
                    Log.w("MpGestureManager", "🎭 FALLBACK: Switching to simulation mode as last resort")
                    startSimulationMode()
                    return
                }
            }
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ MODEL ERROR: Failed to initialize MediaPipe options: ${e.message}")
            Log.e("MpGestureManager", "❌ MODEL ERROR: Stack trace:", e)
            // Ne pas continuer si les options ne peuvent pas être initialisées
            return
        }

        // CAMÉRA + ANALYSE D'IMAGES
        cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
        cameraProviderFuture?.addListener({
            try {
                val provider = cameraProviderFuture?.get() ?: return@addListener
                provider.unbindAll()

                // Configuration de l'analyse d'images optimisée pour une vitesse maximale
                val imageAnalysis = ImageAnalysis.Builder()
                    .setTargetResolution(Size(256, 256))  // Résolution carrée compacte pour un traitement ultra-rapide
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    // Utiliser le format RGBA_8888 qui est compatible avec MediaImageBuilder
                    .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888)
                    .build()

                // Ajouter un Preview pour s'assurer que la caméra est correctement initialisée
                val preview = Preview.Builder()
                    .setTargetResolution(Size(320, 240))  // Résolution plus faible pour un traitement plus rapide
                    .build()

                // Utiliser un exécuteur dédié pour l'analyse d'images
                Log.d("MpGestureManager", "🔧 SETUP: Configuring image analyzer...")
                imageAnalysis.setAnalyzer(cameraExecutor) { imageProxy ->
                    Log.d("MpGestureManager", "🧪 ANALYSE: Frame reçu - Format: ${imageProxy.format}, Size: ${imageProxy.width}x${imageProxy.height}, Timestamp: ${System.currentTimeMillis()}")
                    try {
                        analyzeFrame(imageProxy)
                    } catch (e: Exception) {
                        Log.e("MpGestureManager", "❌ ERREUR lors de l'analyse du frame: ${e.message}")
                        e.printStackTrace()
                        imageProxy.close() // Fermer en cas d'erreur
                    }
                }
                Log.d("MpGestureManager", "✅ SETUP: Image analyzer configured successfully")

                // Sélectionner la caméra en fonction de la variable useFrontCamera
                val cameraSelector = try {
                    if (useFrontCamera && provider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
                        Log.d("MpGestureManager", "✅ Caméra frontale sélectionnée")
                        CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                            .build()
                    } else if (!useFrontCamera && provider.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA)) {
                        Log.d("MpGestureManager", "✅ Caméra arrière sélectionnée")
                        CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                            .build()
                    } else if (provider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
                        Log.w("MpGestureManager", "⚠️ Caméra demandée non disponible, utilisation de la caméra frontale")
                        CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                            .build()
                    } else {
                        Log.w("MpGestureManager", "⚠️ Caméra demandée non disponible, utilisation de la caméra arrière")
                        CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                            .build()
                    }
                } catch (e: Exception) {
                    Log.e("MpGestureManager", "❌ Erreur lors de la sélection de la caméra: ${e.message}")
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                }

                analysis = imageAnalysis

                try {
                    Log.d("MpGestureManager", "🔍 TENTATIVE: Liaison de la caméra au cycle de vie de l'activité...")
                    // Lier à la fois l'analyseur et le preview pour s'assurer que la caméra est correctement initialisée
                    provider.bindToLifecycle(activity, cameraSelector, preview, imageAnalysis)
                    Log.d("MpGestureManager", "✅ SUCCÈS: Caméra liée avec succès au cycle de vie de l'activité")
                    Log.d("MpGestureManager", "📷 INFO: Type de caméra utilisée: ${if (useFrontCamera) "Frontale" else "Arrière"}")

                    // Vérification supplémentaire pour s'assurer que la caméra est bien active
                    try {
                        val cameraInfo = provider.getCameraInfo(cameraSelector)
                        Log.d("MpGestureManager", "📷 INFO: Caméra info obtenue avec succès")
                    } catch (e: Exception) {
                        Log.e("MpGestureManager", "❌ ERREUR: Impossible d'obtenir les informations de la caméra: ${e.message}")
                    }
                } catch (e: Exception) {
                    Log.e("MpGestureManager", "❌ ERREUR: Échec de la liaison de la caméra au cycle de vie: ${e.message}")
                    Log.e("MpGestureManager", "🔍 DEBUG: Stack trace:", e)
                }
            } catch (e: Exception) {
                Log.e("MpGestureManager", "❌ ERREUR: Exception dans la configuration de la caméra: ${e.message}")
                e.printStackTrace()
            }
        }, ContextCompat.getMainExecutor(activity))
    }









    private fun analyzeFrame(image: ImageProxy) {
        try {
            val recognizer = gestureRecognizer ?: run {
                Log.w("MpGestureManager", "⚠️ MODÈLE: non chargé — skip frame")
                image.close()
                return
            }

            // Log TOUTES les frames pour debug
            Log.d("MpGestureManager", "🔍 FRAME: Analyzing frame #$frameCount fmt=${image.format} ${image.width}x${image.height} planes=${image.planes.size}")
            frameCount++

            // Essayer MediaImage d'abord, puis fallback vers Bitmap si ça échoue
            val mpImage: MPImage = try {
                // 1) Essayer MediaImage (flux YUV), chemin zéro-copie
                image.image?.let { mediaImage ->
                    Log.d("MpGestureManager", "🔄 Trying MediaImage conversion...")
                    MediaImageBuilder(mediaImage).build()
                } ?: throw Exception("MediaImage is null")
            } catch (e: Exception) {
                Log.w("MpGestureManager", "⚠️ MediaImage failed: ${e.message}, trying Bitmap conversion...")
                // 2) Fallback vers Bitmap (RGBA_8888)
                val bmp = imageProxyToBitmapRGBA(image) ?: run {
                    Log.e("MpGestureManager", "❌ Both MediaImage and Bitmap conversion failed")
                    image.close()
                    return
                }
                Log.d("MpGestureManager", "✅ Using Bitmap conversion: ${bmp.width}x${bmp.height}")
                BitmapImageBuilder(bmp).build()
            }

            val imageOptions = ImageProcessingOptions.builder()
                .setRotationDegrees(image.imageInfo.rotationDegrees)
                .build()

            val ts = SystemClock.uptimeMillis()

            // Essayer recognizeAsync avec MediaImage, fallback vers Bitmap si ça échoue
            try {
                recognizer.recognizeAsync(mpImage, imageOptions, ts)
            } catch (e: IllegalArgumentException) {
                if (e.message?.contains("buffer should be") == true) {
                    Log.w("MpGestureManager", "⚠️ Buffer size error with MediaImage, trying Bitmap fallback...")
                    // Forcer la conversion Bitmap
                    val bmp = imageProxyToBitmapRGBA(image) ?: run {
                        Log.e("MpGestureManager", "❌ Bitmap fallback failed")
                        return
                    }
                    Log.d("MpGestureManager", "✅ Using Bitmap fallback: ${bmp.width}x${bmp.height}")
                    val bitmapMpImage = BitmapImageBuilder(bmp).build()
                    recognizer.recognizeAsync(bitmapMpImage, imageOptions, ts)
                } else {
                    throw e // Re-throw si ce n'est pas le problème de buffer
                }
            }

        } catch (e: Throwable) {
            Log.e("MpGestureManager", "❌ ERREUR analyse: ${e.message}", e)
        } finally {
            // Toujours relâcher la frame
            try { image.close() } catch (_: Exception) {}
        }
    }

    /**
    * Conversion RGBA_8888 -> Bitmap depuis ImageProxy.
    * Utilisée quand image.image == null (sortie CameraX RGBA).
    */
    private fun imageProxyToBitmapRGBA(image: ImageProxy): Bitmap? {
        return try {
            val plane = image.planes.firstOrNull() ?: return null
            val buffer = plane.buffer
            buffer.rewind()

            val pixelStride = plane.pixelStride // attendu: 4 en RGBA_8888
            val rowStride = plane.rowStride
            val width = image.width
            val height = image.height

            // Largeur du buffer en pixels (rowStride / pixelStride) peut être >= width
            val rowPixels = rowStride / pixelStride

            // On crée un bitmap tampon avec la largeur "rowPixels", puis on crop à 'width'
            val tmp = Bitmap.createBitmap(rowPixels, height, Bitmap.Config.ARGB_8888)
            tmp.copyPixelsFromBuffer(buffer)

            // Recadre à la vraie largeur si besoin
            if (rowPixels != width) {
                Bitmap.createBitmap(tmp, 0, 0, width, height).also { tmp.recycle() }
            } else {
                tmp
            }
        } catch (t: Throwable) {
            Log.e("MpGestureManager", "❌ imageProxyToBitmapRGBA: ${t.message}", t)
            null
        }
    }














    // Convertir ImageProxy en Bitmap
    private fun imageProxyToByteArray(image: ImageProxy): ByteArray? {
        return try {
            // Si le format est RGBA_8888, nous pouvons obtenir le bitmap directement
            if (image.format == 0x1) { // 0x1 est la valeur de ImageFormat.RGBA_8888
                val buffer = image.planes[0].buffer
                val width = image.width
                val height = image.height
                val pixelCount = width * height
                val pixels = IntArray(pixelCount)
                buffer.asIntBuffer().get(pixels, 0, pixelCount)
                
                val bitmap = android.graphics.Bitmap.createBitmap(pixels, width, height, android.graphics.Bitmap.Config.ARGB_8888)
                val stream = ByteArrayOutputStream()
                bitmap.compress(android.graphics.Bitmap.CompressFormat.JPEG, 100, stream)
                return stream.toByteArray()
            } else {
                // Pour les autres formats, utiliser la conversion YUV
                val yBuffer = image.planes[0].buffer
                val uBuffer = image.planes[1].buffer
                val vBuffer = image.planes[2].buffer
                
                val ySize = yBuffer.remaining()
                val uSize = uBuffer.remaining()
                val vSize = vBuffer.remaining()
                
                val nv21 = ByteArray(ySize + uSize + vSize)
                
                yBuffer.get(nv21, 0, ySize)
                uBuffer.get(nv21, ySize, uSize)
                vBuffer.get(nv21, ySize + uSize, vSize)
                
                val yuvImage = YuvImage(nv21, 0x11, image.width, image.height, null) // 0x11 est la valeur de ImageFormat.NV21
                val out = ByteArrayOutputStream()
                // Améliorer la qualité de compression pour une meilleure détection
                yuvImage.compressToJpeg(android.graphics.Rect(0, 0, image.width, image.height), 100, out)
                
                return out.toByteArray()
            }
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ ERREUR lors de la conversion ImageProxy vers ByteArray: ${e.message}")
            e.printStackTrace()
            null
        }
    }
    
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap? {
        return try {
            // Si le format est RGBA_8888, nous pouvons obtenir le bitmap directement
            if (image.format == 0x1) { // 0x1 est la valeur de ImageFormat.RGBA_8888
                val buffer = image.planes[0].buffer
                val width = image.width
                val height = image.height
                val pixelStride = image.planes[0].pixelStride
                val rowStride = image.planes[0].rowStride

                // Créer un bitmap à partir du buffer
                val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                buffer.rewind()
                
                // Copier les pixels manuellement pour éviter les problèmes de stride
                val pixels = IntArray(width * height)
                for (y in 0 until height) {
                    for (x in 0 until width) {
                        val index = y * width + x
                        val bufferIndex = y * rowStride + x * pixelStride
                        pixels[index] = buffer.getInt(bufferIndex)
                    }
                }
                bitmap.setPixels(pixels, 0, width, 0, 0, width, height)

                // Ne pas appliquer de rotation manuelle - laisser MediaPipe gérer l'orientation
                // val matrix = Matrix()
                // matrix.postRotate(image.imageInfo.rotationDegrees.toFloat())
                // val rotatedBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
                val rotatedBitmap = bitmap

                // Libérer le bitmap original
                if (bitmap != rotatedBitmap) {
                    bitmap.recycle()
                }

                rotatedBitmap
            } else {
                // Pour les autres formats, utiliser la conversion YUV
                convertYuvToRgb(image)
            }
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ ERREUR lors de la conversion ImageProxy en Bitmap: ${e.message}")
            e.printStackTrace()
            null
        }
    }

    // Convertir YUV en RGB
    private fun convertYuvToRgb(image: ImageProxy): Bitmap {
        Log.d("MpGestureManager", "🔄 CONVERSION: Conversion YUV vers RGB - Format: ${image.format}, Taille: ${image.width}x${image.height}")
        
        val yBuffer = image.planes[0].buffer
        val uBuffer = image.planes[1].buffer
        val vBuffer = image.planes[2].buffer

        val ySize = yBuffer.remaining()
        val uSize = uBuffer.remaining()
        val vSize = vBuffer.remaining()

        val nv21 = ByteArray(ySize + uSize + vSize)

        yBuffer.get(nv21, 0, ySize)
        uBuffer.get(nv21, ySize, uSize)
        vBuffer.get(nv21, ySize + uSize, vSize)

        val yuvImage = YuvImage(nv21, 0x11, image.width, image.height, null) // 0x11 est la valeur de ImageFormat.NV21
        val out = ByteArrayOutputStream()
        // Améliorer la qualité de compression pour une meilleure détection
        yuvImage.compressToJpeg(android.graphics.Rect(0, 0, image.width, image.height), 100, out)

        val imageBytes = out.toByteArray()
        var bitmap = android.graphics.BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)

        // Rotation et miroir du bitmap pour la caméra frontale
        val matrix = Matrix()
        Log.d("MpGestureManager", "🔄 CONVERSION: Conservation de l'image brute pour MediaPipe")
        // Pour caméra frontale: rotation de 270° et miroir horizontal
        // matrix.postRotate(270f) - Commenté pour éviter la double transformation
        // matrix.preScale(-1f, 1f) - Commenté pour éviter la double transformation
        
        // Redimensionner l'image à 320x240 pour un traitement ultra-rapide
        val targetWidth = 320
        val targetHeight = 240
        val rotatedBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, false)  // Ne pas filtrer pour plus de vitesse
        
        // Libérer le bitmap original pour économiser de la mémoire
        if (bitmap != rotatedBitmap) {
            bitmap.recycle()
        }
        
        Log.d("MpGestureManager", "🔄 CONVERSION: Conversion terminée - Taille finale: ${rotatedBitmap.width}x${rotatedBitmap.height}")
        return rotatedBitmap
    }

    // Copier un asset vers un fichier
    private fun copyAssetToFile(assetPath: String, targetFile: File) {
        try {
            val inputStream: InputStream = activity.assets.open(assetPath)
            val outputStream: OutputStream = FileOutputStream(targetFile)

            val buffer = ByteArray(1024)
            var read: Int
            while (inputStream.read(buffer).also { read = it } != -1) {
                outputStream.write(buffer, 0, read)
            }

            inputStream.close()
            outputStream.flush()
            outputStream.close()
            Log.d("MpGestureManager", "✅ Asset copié avec succès: $assetPath -> ${targetFile.absolutePath}")
        } catch (e: Exception) {
            Log.e("MpGestureManager", "❌ Erreur lors de la copie de l'asset: ${e.message}")
        }
    }



    private var simulationHandler: android.os.Handler? = null
    private var simulationRunnable: Runnable? = null

    private fun startSimulationMode() {
        Log.d("MpGestureManager", "🎭 SIMULATION: Starting gesture simulation mode")

        simulationHandler = android.os.Handler(android.os.Looper.getMainLooper())
        simulationRunnable = object : Runnable {
            override fun run() {
                // Simuler des gestes aléatoirement
                val gestures = arrayOf("Closed_Fist", "Open_Palm", "Victory")
                val randomGesture = gestures[System.currentTimeMillis().toInt() % gestures.size]
                val randomConfidence = 0.7 + (System.currentTimeMillis() % 30) / 100.0
                val randomHand = if (System.currentTimeMillis() % 2 == 0L) "Left" else "Right"

                Log.d("MpGestureManager", "🎭 SIMULATION: Generated gesture: $randomGesture, confidence: $randomConfidence")

                // Envoyer le résultat simulé à Flutter
                activity.runOnUiThread {
                    try {
                        sink?.success(mapOf(
                            "label" to randomGesture,
                            "score" to randomConfidence,
                            "hand" to randomHand,
                            "ts" to System.currentTimeMillis(),
                            "simulated" to true
                        ))
                        Log.d("MpGestureManager", "✅ SIMULATION: Sent simulated gesture to Flutter")
                    } catch (e: Exception) {
                        Log.e("MpGestureManager", "❌ SIMULATION ERROR: ${e.message}")
                    }
                }

                // Programmer le prochain geste simulé
                simulationHandler?.postDelayed(this, 3000) // Toutes les 3 secondes
            }
        }

        // Démarrer la simulation
        simulationHandler?.postDelayed(simulationRunnable!!, 1000) // Démarrer après 1 seconde
    }

    private fun stopSimulationMode() {
        simulationHandler?.removeCallbacks(simulationRunnable!!)
        simulationHandler = null
        simulationRunnable = null
        Log.d("MpGestureManager", "🎭 SIMULATION: Stopped simulation mode")
    }
}
