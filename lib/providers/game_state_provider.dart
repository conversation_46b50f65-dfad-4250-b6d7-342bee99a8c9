import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:audioplayers/audioplayers.dart';

class GameStateProvider extends ChangeNotifier {
  // Game state variables
  String _currentPlayer = "";
  bool _playerSelected = false;
  int _initialLives = 3;
  int _playerLives = 3;
  int _programLives = 3;
  int _scorePlayer = 0;
  int _scoreProgram = 0;
  int _playerJackpotPoints = 0;
  int _programJackpotPoints = 0;
  int _consecutiveWinsPlayer = 0;
  int _consecutiveWinsProgram = 0;
  int _minutes = 0;
  int _seconds = 0;
  
  // Game settings
  String _difficulty = "medium";
  String _bgImage = "assets/bg_img/bg_img_3.webp";
  String? _bgMusic = "sunrise1.mp3"; // Default background music
  String _sineshifterSound = "watt.mp3";
  int _volumeLevel = 50;
  
  // Audio players
  final AudioPlayer _backgroundMusicPlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();
  
  // Timer
  Timer? _gameTimer;
  
  // Players data
  Map<String, dynamic> _playersData = {"players": {}};

  // Constructor to initialize audio volume
  GameStateProvider() {
    // Initialize audio in the background to avoid blocking the constructor
    _initializeAudio();
    // Load saved settings on startup
    _loadSettingsOnStartup();
  }

  // Load settings from storage at startup
  Future<void> _loadSettingsOnStartup() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/settings.json');
      
      if (await file.exists()) {
        String contents = await file.readAsString();
        Map<String, dynamic> settings = json.decode(contents);
        
        // Load saved settings into GameStateProvider
        _volumeLevel = settings["volume"] ?? 50;
        _bgMusic = settings["bg_music"] ?? "sunrise1.mp3";
        _difficulty = settings["difficulty"] ?? "medium";
        _initialLives = settings["lives"] ?? 3;
        _playerLives = _initialLives;
        _programLives = _initialLives;
        _sineshifterSound = settings["sineshifter_sound"] ?? "watt.mp3";
        
        if (settings["bg_image"] != null) {
          _bgImage = 'assets/bg_img/${settings["bg_image"]}';
        }
        
        debugPrint('✅ STARTUP: Settings loaded - Music: $_bgMusic, Volume: $_volumeLevel%, Lives: $_initialLives');
        notifyListeners();
        
        // Start background music after settings are loaded
        if (_bgMusic != null) {
          Future.delayed(const Duration(milliseconds: 1000), () {
            _playBackgroundMusic();
          });
        }
      } else {
        debugPrint('📂 STARTUP: No saved settings found, using defaults');
        // Start default music if no settings exist
        if (_bgMusic != null) {
          Future.delayed(const Duration(milliseconds: 1000), () {
            _playBackgroundMusic();
          });
        }
      }
    } catch (e) {
      debugPrint('🔴 STARTUP ERROR: Failed to load settings: $e');
      // Fallback to default music on error
      if (_bgMusic != null) {
        Future.delayed(const Duration(milliseconds: 1000), () {
          _playBackgroundMusic();
        });
      }
    }
  }

  // Initialize audio players with proper volume
  Future<void> _initializeAudio() async {
    await _setVolume(); // Set initial volume for both players

    // Configurer les modes de release pour les lecteurs
    await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
    await _effectsPlayer.setReleaseMode(ReleaseMode.release);

    debugPrint('🔊 AUDIO: Audio players initialized');
  }

  // Getters
  String get currentPlayer => _currentPlayer;
  bool get playerSelected => _playerSelected;
  int get initialLives => _initialLives;
  int get playerLives => _playerLives;
  int get programLives => _programLives;
  int get scorePlayer => _scorePlayer;
  int get scoreProgram => _scoreProgram;
  int get playerJackpotPoints => _playerJackpotPoints;
  int get programJackpotPoints => _programJackpotPoints;
  int get consecutiveWinsPlayer => _consecutiveWinsPlayer;
  int get consecutiveWinsProgram => _consecutiveWinsProgram;
  int get minutes => _minutes;
  int get seconds => _seconds;
  String get difficulty => _difficulty;
  String get bgImage => _bgImage;
  String? get bgMusic => _bgMusic;
  String get sineshifterSound => _sineshifterSound;
  int get volumeLevel => _volumeLevel;
  Map<String, dynamic> get playersData => _playersData;

  // Game initialization
  void setupGame() {
    _playerLives = _initialLives;
    _programLives = _initialLives;
    _scorePlayer = 0;
    _scoreProgram = 0;
    _playerJackpotPoints = 0;
    _programJackpotPoints = 0;
    _consecutiveWinsPlayer = 0;
    _consecutiveWinsProgram = 0;
    
    // Start background music when game starts
    debugPrint('🎮 GAME: Setting up game - Current music: $_bgMusic');
    if (_bgMusic != null) {
      debugPrint('🎮 GAME: Starting background music from setupGame()');
      _playBackgroundMusic();
    } else {
      debugPrint('🔴 GAME: No background music set, using default');
      _bgMusic = "sunrise1.mp3";
      _playBackgroundMusic();
    }
    
    notifyListeners();
  }

  // Timer methods
  void startTimer() {
    _minutes = 0;
    _seconds = 0;
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _seconds++;
      if (_seconds == 60) {
        _seconds = 0;
        _minutes++;
      }
      notifyListeners();
    });
  }

  void stopTimer() {
    _gameTimer?.cancel();
  }

  void resetTimer() {
    _gameTimer?.cancel();
    _minutes = 0;
    _seconds = 0;
    notifyListeners();
  }

  // Player management
  void selectPlayer(String playerName) {
    _currentPlayer = playerName;
    _playerSelected = true;
    notifyListeners();
  }

  Future<void> addNewPlayer(String playerName) async {
    if (playerName.isNotEmpty && !_playersData['players'].containsKey(playerName)) {
      _playersData['players'][playerName] = [];
      await _savePlayersData();
      selectPlayer(playerName);
    }
  }

  Future<void> deletePlayer(String playerName) async {
    if (_playersData['players'].containsKey(playerName)) {
      _playersData['players'].remove(playerName);
      await _savePlayersData();
      
      if (_currentPlayer == playerName) {
        _currentPlayer = "";
        _playerSelected = false;
      }
      notifyListeners();
    }
  }

  // Settings application
  void applySettings({
    int? lives,
    String? difficulty,
    String? bgMusic,
    String? bgImage,
    String? sineshifterSound,
    int? volume,
    bool saveToStorage = true, // Add flag to save settings automatically
  }) {
    debugPrint('⚙️ SETTINGS: Applying settings...');
    bool musicChanged = false;
    
    if (lives != null) {
      _initialLives = lives;
      _playerLives = lives;
      _programLives = lives;
      debugPrint('⚙️ SETTINGS: Lives set to $lives');
    }
    if (difficulty != null) {
      _difficulty = difficulty;
      debugPrint('⚙️ SETTINGS: Difficulty set to $difficulty');
    }
    if (bgMusic != null) {
      debugPrint('🎵 SETTINGS: Background music changing from "$_bgMusic" to "$bgMusic"');
      _bgMusic = bgMusic;
      musicChanged = true;
      debugPrint('🎵 SETTINGS: Music changed flag set to true');
    }
    if (bgImage != null) {
      String fullPath = 'assets/bg_img/$bgImage';
      debugPrint('🖼️ SETTINGS: Background image set to $fullPath');
      _bgImage = fullPath;
    }
    if (sineshifterSound != null) {
      _sineshifterSound = sineshifterSound;
      debugPrint('🔊 SETTINGS: Sineshifter sound set to $sineshifterSound');
    }
    if (volume != null) {
      debugPrint('🔈 SETTINGS: Volume changing from $_volumeLevel% to $volume%');
      _volumeLevel = volume;
      _setVolume();
    }
    
    notifyListeners();
    debugPrint('✅ SETTINGS: All settings applied successfully');
    
    // Start/restart background music if it changed
    if (musicChanged && _bgMusic != null) {
      debugPrint('🎵 SETTINGS: Starting background music: $_bgMusic');
      _playBackgroundMusic();
    }
    
    // Automatically save settings to storage
    if (saveToStorage) {
      _saveCurrentSettings();
    }
  }
  
  // Save current settings to storage
  Future<void> _saveCurrentSettings() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/settings.json');
      
      Map<String, dynamic> settings = {
        "volume": _volumeLevel,
        "bg_music": _bgMusic,
        "bg_image": _bgImage?.replaceAll('assets/bg_img/', ''), // Save just filename
        "sineshifter_sound": _sineshifterSound,
        "lives": _initialLives,
        "difficulty": _difficulty,
      };
      
      await file.writeAsString(json.encode(settings));
      debugPrint('💾 SAVE: Settings saved to storage successfully');
    } catch (e) {
      debugPrint('🔴 SAVE ERROR: Failed to save settings: $e');
    }
  }

  // Method to test if background music can be played
  Future<bool> testBackgroundMusic(String musicFile) async {
    try {
      debugPrint('🎧 MUSIC TEST: Testing music file: $musicFile');
      await _backgroundMusicPlayer.stop();
      // Normaliser le chemin du fichier
      String assetPath = 'assets/bg_music/$musicFile';
      await _backgroundMusicPlayer.play(AssetSource(assetPath));
      await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
      await _setVolume();
      debugPrint('✅ MUSIC TEST: Successfully playing $musicFile');
      return true;
    } catch (e) {
      debugPrint('🔴 MUSIC TEST: Failed to play $musicFile: $e');
      return false;
    }
  }

  // Audio methods
  Future<void> _playBackgroundMusic() async {
    if (_bgMusic != null) {
      try {
        debugPrint('🎵 MUSIC: Attempting to play background music: $_bgMusic');
        debugPrint('🎵 MUSIC: Full path: bg_music/$_bgMusic');
        
        await _backgroundMusicPlayer.stop();
        debugPrint('🎵 MUSIC: Stopped previous music');
        
        await _backgroundMusicPlayer.play(AssetSource('bg_music/$_bgMusic'));
        debugPrint('🎵 MUSIC: Started playing: $_bgMusic');
        
        await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
        debugPrint('🎵 MUSIC: Set to loop mode');
        
        await _setVolume();
        debugPrint('🎵 MUSIC: Volume set to ${_volumeLevel}%');
        
        debugPrint('✅ MUSIC: Background music setup complete: $_bgMusic');
      } catch (e) {
        debugPrint('🔴 MUSIC: Error playing background music $_bgMusic: $e');
        debugPrint('🔴 MUSIC: Attempted path: bg_music/$_bgMusic');
        
        // Try to play default music if the selected one fails
        if (_bgMusic != 'sunrise1.mp3') {
          debugPrint('🔄 MUSIC: Attempting fallback to default music: sunrise1.mp3');
          try {
            await _backgroundMusicPlayer.stop();
            await _backgroundMusicPlayer.play(AssetSource('bg_music/sunrise1.mp3'));
            await _backgroundMusicPlayer.setReleaseMode(ReleaseMode.loop);
            await _setVolume();
            debugPrint('✅ MUSIC: Fallback music started successfully');
          } catch (fallbackError) {
            debugPrint('🔴 MUSIC: Even fallback music failed: $fallbackError');
          }
        }
      }
    } else {
      debugPrint('⚠️ MUSIC: No background music selected (_bgMusic is null)');
    }
  }

  Future<void> _setVolume() async {
    double volume = _volumeLevel / 100.0;
    await _backgroundMusicPlayer.setVolume(volume);
    await _effectsPlayer.setVolume(volume); // Also set volume for effects
  }

  Future<void> playEffect(String soundFile) async {
    try {
      debugPrint('🔊 AUDIO: Playing sound effect: $soundFile');
      await _effectsPlayer.play(AssetSource(soundFile));
      debugPrint('✅ AUDIO: Sound effect started successfully');
    } catch (e) {
      debugPrint('🔴 AUDIO: Error playing effect: $e');
    }
  }

  // Game logic methods
  void updateScore({required bool playerWon}) {
    if (playerWon) {
      _scorePlayer++;
      _consecutiveWinsPlayer++;
      _consecutiveWinsProgram = 0;
      _handleJackpotPoints(winner: "player");
    } else {
      _scoreProgram++;
      _consecutiveWinsProgram++;
      _consecutiveWinsPlayer = 0;
      _handleJackpotPoints(winner: "program");
    }
    notifyListeners();
  }

  void updateLives({required bool playerLostLife}) {
    if (playerLostLife) {
      _playerLives--;
      // Check if player can buy extra life AFTER losing the life
      if (_playerLives <= 0 && _playerJackpotPoints >= 3000) {
        debugPrint('🎆 JACKPOT: Player lost last life but has ${_playerJackpotPoints} jackpot points - buying extra life!');
        _buyExtraLife(isPlayer: true);
      }
    } else {
      _programLives--;
      // Check if program can buy extra life AFTER losing the life
      if (_programLives <= 0 && _programJackpotPoints >= 3000) {
        debugPrint('🎆 JACKPOT: Program lost last life but has ${_programJackpotPoints} jackpot points - buying extra life!');
        _buyExtraLife(isPlayer: false);
      }
    }
    notifyListeners();
  }

  void _handleJackpotPoints({required String winner}) {
    if (winner == "player") {
      if (_consecutiveWinsPlayer == 1) {
        _playerJackpotPoints += 100;
      } else if (_consecutiveWinsPlayer >= 2) {
        _playerJackpotPoints += 2500;
        // Play jackpot sound for big points bonus
        playEffect('assets/audio/jackpot_start.mp3');
        debugPrint('🎉 JACKPOT: Player earned 2500 jackpot points!');
      }
    } else {
      if (_consecutiveWinsProgram == 1) {
        _programJackpotPoints += 100;
      } else if (_consecutiveWinsProgram >= 2) {
        _programJackpotPoints += 2500;
        // Play jackpot sound for big points bonus
        playEffect('assets/audio/jackpot_start.mp3');
        debugPrint('🎉 JACKPOT: Program earned 2500 jackpot points!');
      }
    }
    notifyListeners();
  }

  void _buyExtraLife({required bool isPlayer}) {
    if (isPlayer && _playerJackpotPoints >= 3000) {
      _playerJackpotPoints -= 3000;
      _playerLives = 1; // Set to 1 life instead of incrementing
      // Play jackpot end sound for buying extra life
      playEffect('assets/audio/jackpot_end.mp3');
      debugPrint('🎆 JACKPOT: Player bought extra life! Lives: $_playerLives, Remaining jackpot: $_playerJackpotPoints');
    } else if (!isPlayer && _programJackpotPoints >= 3000) {
      _programJackpotPoints -= 3000;
      _programLives = 1; // Set to 1 life instead of incrementing
      // Play jackpot end sound for buying extra life
      playEffect('assets/audio/jackpot_end.mp3');
      debugPrint('🎆 JACKPOT: Program bought extra life! Lives: $_programLives, Remaining jackpot: $_programJackpotPoints');
    }
    notifyListeners();
  }

  // Save/Load game results
  Future<void> saveGameResult({
    required String finalResult,
    required int playerPoints,
    required int programPoints,
    required int playerJackpot,
    required int programJackpot,
  }) async {
    if (_currentPlayer.isNotEmpty) {
      String elapsedTime = "${_minutes.toString().padLeft(2, '0')}:${_seconds.toString().padLeft(2, '0')}";
      
      Map<String, dynamic> gameData = {
        "result": finalResult,
        "player_points": playerPoints,
        "program_points": programPoints,
        "player_jackpot": playerJackpot,
        "program_jackpot": programJackpot,
        "elapsed_time": elapsedTime,
        "timing": DateTime.now().toString().substring(0, 19),
      };
      
      if (!_playersData["players"].containsKey(_currentPlayer)) {
        _playersData["players"][_currentPlayer] = [];
      }
      _playersData["players"][_currentPlayer].add(gameData);
      
      await _savePlayersData();
    }
  }

  // File operations
  Future<void> loadPlayersData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/result.json');
      
      if (await file.exists()) {
        String contents = await file.readAsString();
        _playersData = json.decode(contents);
      }
    } catch (e) {
      debugPrint('Error loading players data: $e');
      _playersData = {"players": {}};
    }
    notifyListeners();
  }

  Future<void> _savePlayersData() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/result.json');
      await file.writeAsString(json.encode(_playersData));
    } catch (e) {
      debugPrint('Error saving players data: $e');
    }
  }

  // Restart game
  void restartGame() {
    debugPrint('🔄 RESTART: Restarting game - Current music: $_bgMusic');
    stopTimer();
    
    // Ensure background music is set before setup
    if (_bgMusic == null) {
      debugPrint('🔄 RESTART: No music set, loading from settings');
      _loadSettingsOnStartup().then((_) {
        setupGame();
        resetTimer();
      });
    } else {
      setupGame();
      resetTimer();
    }
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    _backgroundMusicPlayer.dispose();
    _effectsPlayer.dispose();
    super.dispose();
  }

}