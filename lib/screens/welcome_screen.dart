import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:io' show Platform;

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _animationStarted = false;
  bool _isAndroid = false;
  @override
  void initState() {
    super.initState();
    _isAndroid = Platform.isAndroid;
    _initializeWithErrorHandling();
  }

  Future<void> _initializeWithErrorHandling() async {
    try {
      // Start audio and animation simultaneously for better synchronization
      if (_isAndroid) {
        // Start both audio and animation together on Android
        _playIntroSound();
        _startAnimation();
      } else {
        // For iOS, load audio first then start animation
        await _playIntroSound();
        _startAnimation();
      }
    } catch (e) {
      debugPrint('Error during welcome screen initialization: $e');
      // Fallback: proceed with animation only
      _startAnimation();
    }
  }

  Future<void> _playIntroSound() async {
    try {
      debugPrint('🔊 WELCOME: Playing intro sound: audio/eddars_global.mp3');
      await _audioPlayer.play(AssetSource('audio/eddars_global.mp3'));
      debugPrint('✅ WELCOME: Intro sound started successfully');
    } catch (e) {
      // Handle audio loading error gracefully
      debugPrint('🔴 WELCOME: Error playing intro sound: $e');
      // Continue without audio - app should not crash
    }
  }

  void _startAnimation() {
    // Start animation after a short delay
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _animationStarted = true;
        });
        debugPrint('✅ WELCOME: Animation started with eddars_global.png');
      }
    });

    // Navigate to main screen after animation completes (adjusted for longer animation)
    Future.delayed(const Duration(milliseconds: 2500), () {
      if (mounted) {
        debugPrint('🎮 WELCOME: Navigating to main screen');
        Navigator.pushReplacementNamed(context, '/main');
      }
    });
  }



  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final imageSize = screenSize.width * 0.8; // Increased from 0.6 to 0.8
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Image.asset(
          'assets/images/eddars_global.png',
          width: imageSize,
          height: imageSize,
          fit: BoxFit.contain, // Ensure proper scaling
          errorBuilder: (context, error, stackTrace) {
            debugPrint('🔴 WELCOME: Error loading eddars_global.png: $error');
            // Fallback if image fails to load
            return Container(
              width: imageSize,
              height: imageSize,
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.sports_esports,
                color: Colors.white,
                size: 80, // Increased icon size
              ),
            );
          },
        )
            .animate(target: _animationStarted ? 1 : 0)
            .scale(
              begin: const Offset(0.2, 0.2), // Start larger (20% instead of 10%)
              end: const Offset(1.0, 1.0),   // End at full size (100% instead of 30%)
              duration: const Duration(milliseconds: 1500), // Slightly longer animation
              curve: Curves.elasticOut, // More dynamic curve
            )
            .fadeOut(
              delay: const Duration(milliseconds: 1200), // Adjusted timing
              duration: const Duration(milliseconds: 300),
            ),
      ),
    );
  }
}