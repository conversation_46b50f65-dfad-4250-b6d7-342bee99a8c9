#!/bin/bash

# Script pour tester sur l'émulateur
# Usage: ./debug_emulator.sh

echo "🔍 DIAGNOSTIC: Test sur émulateur"
echo "================================="

# Identifier l'émulateur
EMULATOR_ID="emulator-5554"

echo "📱 Émulateur détecté: $EMULATOR_ID"
echo ""

# Installer l'APK sur l'émulateur
echo "📲 Installation de l'APK sur l'émulateur..."
adb -s $EMULATOR_ID install -r build/app/outputs/flutter-apk/app-debug.apk

echo ""
echo "🚀 Lancement de l'application..."
adb -s $EMULATOR_ID shell am start -n com.eddars.rockpaperscissors/.MainActivity

echo ""
echo "🚀 INSTRUCTIONS:"
echo "1. L'application devrait s'ouvrir sur l'émulateur"
echo "2. Allez dans 'Gesture Training'"
echo "3. C<PERSON><PERSON> sur 'Start Training' (ne devrait plus crasher)"
echo "4. Vous devriez voir des gestes simulés"
echo ""
echo "📋 Logs en temps réel (Ctrl+C pour arrêter):"
echo "=============================================="

# Nettoyer les logs et capturer ceux de l'émulateur
adb -s $EMULATOR_ID logcat -c
adb -s $EMULATOR_ID logcat | grep -E "(MpGestureManager|MainActivity|CAMERA|GESTURE|PERMISSION|🎯|📷|✅|❌|🔴|RockPaperScissors|SIMULATION|EMULATOR)" --color=always
